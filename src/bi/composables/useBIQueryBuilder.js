import IconHawkArrowDown from '~icons/hawk/arrow-down';
import IconHawk<PERSON>rrowUp from '~icons/hawk/arrow-up';
import calendar from '~icons/hawk/calendar';
import IconHawkFormula from '~icons/hawk/formula';
import IconHawkFunction from '~icons/hawk/function';
import IconHawkHashTwo from '~icons/hawk/hash-two';
import IconHawkInnerJoin from '~icons/hawk/inner-join';
import IconHawkOuterJoin from '~icons/hawk/outer-join';
import IconHawkRightJoin from '~icons/hawk/right-join';
import IconHawkTypeOne from '~icons/hawk/type-one';
import IconHawkLeftJoin from '~icons/hawk/vector-join';
import { useCommonImports } from '~/common/composables/common-imports.composable';

function getIconsForType(type) {
  const icons_type_map = {
    string: IconHawkTypeOne,
    numeric: IconHawkHashTwo,
    float: IconHawkHashTwo,
    integer: IconHawkHashTwo,
    date: calendar,
    function: IconHawkFunction,
    formula: IconHawkFormula,
    ascending: IconHawkArrowUp,
    descending: IconHawkArrowDown,
    joins: {
      inner: IconHawkInnerJoin,
      outer: IconHawkOuterJoin,
      right: IconHawkRightJoin,
      left: IconHawkLeftJoin,
    },
  };
  return icons_type_map[type] || IconHawkTypeOne;
}

function getOperatorsForType(type) {
  const operators_type_map = {
    text: [{ label: 'Concat', output_type: 'text' }],
    numeric: [{ label: 'Sum', output_type: 'numeric' }, { label: 'Avg', output_type: 'numeric' }, { label: 'Max', output_type: 'numeric' }, { label: 'Min', output_type: 'numeric' }, { label: 'Count', output_type: 'numeric' }],
    date: [{ label: 'Min', output_type: 'date' }, { label: 'Max', output_type: 'date' }],
  };

  const operators_for_type = {
    integer: operators_type_map.numeric,
    float: operators_type_map.numeric,
    date: operators_type_map.date,
    text: operators_type_map.text,
    numeric: operators_type_map.numeric,
    timestamp: operators_type_map.date,
  };

  return operators_for_type[type] || [];
}

export function useBIQueryBuilder() {
  const { $t } = useCommonImports();
  const constructFieldLabel = field => field.label || (field.operator ? `${field.table.label} -> ${field.operator.label} ${$t('of')} ${field.column.label}` : `${field.table.label} -> ${field.column.label}`);

  return {
    constructFieldLabel,
    getIconsForType,
    getOperatorsForType,
  };
}
