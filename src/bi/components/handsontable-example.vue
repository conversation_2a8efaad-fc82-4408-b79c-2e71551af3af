<script setup>
import { faker } from '@faker-js/faker';
import { ref } from 'vue';
import BiHandsontable from './bi-handsontable.vue';

// Sample data
const tableData = ref([
  { id: '66277431', activity: 'Install formwork and reinforcing for site paving', subactivity: 'EXPL - Assessment', workDone: '647', date: 'August 7, 2017' },
  { id: '55700223', activity: 'Install temporary facilities and controls', subactivity: 'ACTDUR - Actual Duration Test', workDone: '154', date: 'April 28, 2016' },
  { id: '55700223', activity: 'Install site furnishings', subactivity: 'W7245 - TW LEV3/Small Group Employment', workDone: '798', date: 'October 31, 2017' },
  { id: '52936567', activity: 'Demolish and remove existing structures', subactivity: 'W7059 - HCH 1:2/In-Home & Community Support', workDone: '826', date: 'July 14, 2015' },
  { id: '38766940', activity: 'Install site signage', subactivity: 'DEVT - Developmental Testing', workDone: '492', date: 'May 20, 2015' },
  { id: '11081197', activity: 'Install temporary facilities and controls', subactivity: 'W2025 - Supported Employment – Job Support', workDone: '196', date: 'November 16, 2014' },
  { id: '66538135', activity: 'Install site lighting and electrical', subactivity: 'W5996 - 100% Community 1:1', workDone: '703', date: 'October 24, 2018' },
  { id: '66538135', activity: 'Backfill foundation and footing trenches', subactivity: 'W1726 - Companion', workDone: '540', date: 'May 6, 2012' },
  { id: '01906912', activity: 'Excavate and prepare building foundation', subactivity: 'W1726 - Companion', workDone: '877', date: 'September 24, 2017' },
  { id: '76031847', activity: 'Place and finish site paving', subactivity: 'W9794 - Job Support', workDone: '922', date: 'May 31, 2015' },
  { id: '37890606', activity: 'Place and finish site paving', subactivity: 'W2025 - Supported Employment – Job Support', workDone: '536', date: 'February 9, 2015' },
  { id: '58276066', activity: 'Install underground utilities', subactivity: 'W7059 - HCH 1:2/In-Home & Community Support', workDone: '423', date: 'August 2, 2013' },
  { id: '01906912', activity: 'Mobilize construction equipment and personnel', subactivity: 'W7059 - HCH 1:2/In-Home & Community Support', workDone: '600', date: 'December 29, 2012' },
  { id: '01906912', activity: 'Place and finish site paving', subactivity: 'W7275 - Transportation Zone 2', workDone: '357', date: 'November 28, 2015' },
  { id: '37890606', activity: 'Install site lighting and electrical', subactivity: 'W7275 - Transportation Zone 2', workDone: '130', date: 'May 12, 2019' },
  { id: '37890606', activity: 'Install temporary facilities and controls', subactivity: 'SUMR - Summer Program', workDone: '883', date: 'March 6, 2018' },
  { id: '37890606', activity: 'Install site curbs and gutters', subactivity: 'W7059 - HCH 1:2/In-Home & Community Support', workDone: '185', date: 'May 9, 2014' },
  { id: '93242854', activity: 'Install site landscaping and irrigation', subactivity: 'W7059 - HCH 1:2/In-Home & Community Support', workDone: '426', date: 'February 29, 2012' },
  { id: '29103050', activity: 'Perform site clean-up and demobilization', subactivity: 'W2025 - Supported Employment – Job Support', workDone: '561', date: 'May 29, 2017' },
  { id: '93242854', activity: 'Install subgrade and base course for site paving', subactivity: 'SUMR - Summer Program', workDone: '740', date: 'October 25, 2019' },
  { id: '93242854', activity: 'Excavate and prepare building foundation', subactivity: 'SICK - Sick Day', workDone: '447', date: 'October 30, 2017' },
  { id: '34034474', activity: 'Install underground utilities', subactivity: 'W1726 - Companion', workDone: '994', date: 'March 23, 2013' },
  { id: '58276066', activity: 'Perform site clean-up and demobilization', subactivity: 'EXPL - Assessment', workDone: '453', date: 'February 11, 2014' },
  { id: '55069827', activity: 'Install site fencing and gates', subactivity: 'SICK - Sick Day', workDone: '816', date: 'February 28, 2018' },
  { id: '43397744', activity: 'Install site fencing and gates', subactivity: 'SICK - Sick Day', workDone: '583', date: 'February 28, 2018' },
  { id: '43397744', activity: 'Install site fencing and gates', subactivity: 'SICK - Sick Day', workDone: '429', date: 'February 28, 2018' },
]);

const tableColumns = ref([
  { data: 'id', title: 'ID', width: 150 },
  { data: 'activity', title: 'Activity', width: 300 },
  { data: 'subactivity', title: 'Subactivity', width: 300 },
  { data: 'workDone', title: 'Work Done', width: 150 },
  { data: 'date', title: 'Date', width: 150 },
]);

// Column configuration for styling
const columnConfig = ref({});
const forceUpdate = ref(1);

// Nested headers example
const nestedHeaders = ref([
  ['', { label: 'Work Details', colspan: 2 }, { label: 'Work Progress', colspan: 2 }],
  ['ID', 'Activity', 'Subactivity', 'Work Done', 'Date'],
]);

// Controls
const showSkeleton = ref(false);
const eventLog = ref([]);

const NUM_ROWS = 1000;
const NUM_COLUMNS = 50;

// Generate fake data with 500 columns per row
function generateFakeData(rows, cols) {
  const data = [];
  for (let i = 0; i < rows; i++) {
    const row = {};
    for (let j = 0; j < cols; j++) {
      row[`col_${j}`] = faker.word.words(1); // short random value for performance
    }
    data.push(row);
  }
  return data;
}

const largeTableData = ref(generateFakeData(NUM_ROWS, NUM_COLUMNS));

// Generate column definitions dynamically
const largeColumns = Array.from({ length: NUM_COLUMNS }, (_, index) => ({
  data: `col_${index}`,
  title: `Column ${index + 1}`,
  width: 150,
}));

// Methods
function toggleSkeletonLoader() {
  showSkeleton.value = !showSkeleton.value;
  forceUpdate.value++;
  addToEventLog(`Skeleton loader ${showSkeleton.value ? 'enabled' : 'disabled'}`);
}

function changeColumnColors() {
  columnConfig.value = {
    subactivity: { backgroundColor: '#fff3e0', sort: 'asc' },
    date: { backgroundColor: '#fff3e0' },
  };
  forceUpdate.value++;
  addToEventLog('Applied column colors');
}

function resetColumnColors() {
  columnConfig.value = {};
  forceUpdate.value++;
  addToEventLog('Reset column colors');
}

function onCellClick(data) {
  addToEventLog(`Cell clicked: Row ${data.row}, Column ${data.column}`);
}

function onColumnResize(data) {
  addToEventLog(`Column ${data.column} resized to ${data.newSize}px`);
}

function onHeaderClick(data) {
  addToEventLog(`Header clicked: Column ${data.column}, Action: ${data.action}`);
}

function addToEventLog(message) {
  const timestamp = new Date().toLocaleTimeString();
  eventLog.value.unshift(`[${timestamp}] ${message}`);

  // Keep only last 10 events
  if (eventLog.value.length > 10) {
    eventLog.value = eventLog.value.slice(0, 10);
  }
}
changeColumnColors();
</script>

<template>
  <div class="handsontable-example">
    <!-- <div class="controls">
      <button @click="toggleSkeletonLoader">
        {{ showSkeleton ? 'Hide' : 'Show' }} Skeleton Loader
      </button>

      <button @click="changeColumnColors">
        Apply Column Colors
      </button>

      <button @click="resetColumnColors">
        Reset Column Colors
      </button>
    </div> -->

    <div class="h-auto">
      <!--
        :data="tableData"
        :columns="tableColumns"
        :nested-headers="nestedHeaders"
      -->
      <BiHandsontable
        :key="forceUpdate"
        height="600px"
        :bi-table-id="`example-table-${forceUpdate}`"
        :data="largeTableData"
        :columns="largeColumns"
        :sort-config="[
          { key: 'activity', sortOrder: 'asc' },
          { key: 'date', sortOrder: 'desc' },
        ]"
        :column-config="columnConfig"
        :row-headers="true"
        :show-skeleton-loader="showSkeleton"
        @column-resize="onColumnResize"
      />
    </div>
  </div>
</template>

<style scoped lang="scss">
.handsontable-example {
  padding: 20px;

  h2 {
    margin-bottom: 20px;
    color: #333;
  }

  .controls {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;

    button {
      padding: 8px 16px;
      background: #007bff;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;

      &:hover {
        background: #0056b3;
      }
    }
  }

  .event-log {
    margin-top: 30px;

    h3 {
      margin-bottom: 10px;
      color: #333;
    }

    .log-entries {
      background: #f8f9fa;
      border: 1px solid #dee2e6;
      border-radius: 4px;
      padding: 15px;
      max-height: 200px;
      overflow-y: auto;

      .log-entry {
        padding: 4px 0;
        font-family: monospace;
        font-size: 12px;
        color: #495057;
        border-bottom: 1px solid #e9ecef;

        &:last-child {
          border-bottom: none;
        }
      }
    }
  }

  .info-section {
    margin-top: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #dee2e6;

    h3 {
      margin-bottom: 15px;
      color: #333;
      font-size: 16px;
    }

    ul {
      margin: 0;
      padding-left: 20px;

      li {
        margin-bottom: 8px;
        color: #495057;
        font-size: 14px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}
</style>
