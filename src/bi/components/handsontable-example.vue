<script setup>
import { ref } from 'vue';
import BiHandsontable from './bi-handsontable.vue';

// Sample data
const tableData = ref([
  { id: '1', activity: 'Site Survey', subactivity: 'Drone Flight', workDone: 'Completed aerial mapping of solar farm section A', date: '2024-01-15' },
  { id: '2', activity: 'Data Analysis', subactivity: 'Image Processing', workDone: 'Processed 150 thermal images for hotspot detection', date: '2024-01-16' },
  { id: '3', activity: 'Report Generation', subactivity: 'Defect Analysis', workDone: 'Identified 12 potential panel defects', date: '2024-01-17' },
  { id: '4', activity: 'Site Survey', subactivity: 'Ground Inspection', workDone: 'Physical verification of flagged panels', date: '2024-01-18' },
  { id: '5', activity: 'Data Analysis', subactivity: 'Performance Analysis', workDone: 'Calculated energy loss due to defects', date: '2024-01-19' },
]);

const tableColumns = ref([
  { data: 'id', title: 'ID', width: 80 },
  { data: 'activity', title: 'Activity', width: 150 },
  { data: 'subactivity', title: 'Subactivity', width: 150 },
  { data: 'workDone', title: 'Work Done', width: 300 },
  { data: 'date', title: 'Date', width: 120 },
]);

// Column configuration for styling
const columnConfig = ref({});

// Controls
const showSkeleton = ref(false);
const eventLog = ref([]);

// Methods
function toggleSkeletonLoader() {
  showSkeleton.value = !showSkeleton.value;
  addToEventLog(`Skeleton loader ${showSkeleton.value ? 'enabled' : 'disabled'}`);
}

function changeColumnColors() {
  columnConfig.value = {
    'activity': { backgroundColor: '#e3f2fd' },
    'subactivity': { backgroundColor: '#f3e5f5' },
    'workDone': { backgroundColor: '#e8f5e8' },
    'date': { backgroundColor: '#fff3e0' },
  };
  addToEventLog('Applied column colors');
}

function resetColumnColors() {
  columnConfig.value = {};
  addToEventLog('Reset column colors');
}

function onCellClick(data) {
  addToEventLog(`Cell clicked: Row ${data.row}, Column ${data.column}`);
}

function onColumnResize(data) {
  addToEventLog(`Column ${data.column} resized to ${data.newSize}px`);
}

function onHeaderClick(data) {
  addToEventLog(`Header clicked: Column ${data.column}, Action: ${data.action}`);
}

function addToEventLog(message) {
  const timestamp = new Date().toLocaleTimeString();
  eventLog.value.unshift(`[${timestamp}] ${message}`);

  // Keep only last 10 events
  if (eventLog.value.length > 10) {
    eventLog.value = eventLog.value.slice(0, 10);
  }
}
</script>

<template>
  <div class="handsontable-example">
    <h2>Handsontable Example</h2>

    <div class="controls">
      <button @click="toggleSkeletonLoader">
        {{ showSkeleton ? 'Hide' : 'Show' }} Skeleton Loader
      </button>

      <button @click="changeColumnColors">
        Apply Column Colors
      </button>

      <button @click="resetColumnColors">
        Reset Column Colors
      </button>
    </div>

    <div class="h-[calc(100%-62px)]">
      <BiHandsontable
        :data="tableData"
        :columns="tableColumns"
        :column-config="columnConfig"
        :show-skeleton-loader="showSkeleton"
        @cell-click="onCellClick"
        @column-resize="onColumnResize"
        @header-click="onHeaderClick"
      />
    </div>
    <div class="info-section">
      <h3>Features Demonstrated:</h3>
      <ul>
        <li>✅ Handsontable v15.3 loaded via CDN</li>
        <li>✅ Column resizing enabled</li>
        <li>✅ Double-click column headers to autofit</li>
        <li>✅ All cells are readonly</li>
        <li>✅ Nested headers support</li>
        <li>✅ Custom cell renderer with column coloring</li>
        <li>✅ Skeleton loader for loading states</li>
        <li>✅ Header icons on hover (Settings ⚙️, Filter 🔍, Sort ↕️)</li>
      </ul>
    </div>

    <div class="event-log">
      <h3>Event Log:</h3>
      <div class="log-entries">
        <div
          v-for="(event, index) in eventLog"
          :key="index"
          class="log-entry"
        >
          {{ event }}
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.handsontable-example {
  padding: 20px;

  h2 {
    margin-bottom: 20px;
    color: #333;
  }

  .controls {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;

    button {
      padding: 8px 16px;
      background: #007bff;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;

      &:hover {
        background: #0056b3;
      }
    }
  }

  .event-log {
    margin-top: 30px;

    h3 {
      margin-bottom: 10px;
      color: #333;
    }

    .log-entries {
      background: #f8f9fa;
      border: 1px solid #dee2e6;
      border-radius: 4px;
      padding: 15px;
      max-height: 200px;
      overflow-y: auto;

      .log-entry {
        padding: 4px 0;
        font-family: monospace;
        font-size: 12px;
        color: #495057;
        border-bottom: 1px solid #e9ecef;

        &:last-child {
          border-bottom: none;
        }
      }
    }
  }

  .info-section {
    margin-top: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #dee2e6;

    h3 {
      margin-bottom: 15px;
      color: #333;
      font-size: 16px;
    }

    ul {
      margin: 0;
      padding-left: 20px;

      li {
        margin-bottom: 8px;
        color: #495057;
        font-size: 14px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}
</style>
