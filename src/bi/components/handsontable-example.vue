<script setup>
import { ref } from 'vue';
import BiHandsontable from './bi-handsontable.vue';

// Sample data
const tableData = ref([
  { id: '66277431', activity: 'Install formwork and reinforcing for site paving', subactivity: 'EXPL - Assessment', workDone: '647', date: 'August 7, 2017' },
  { id: '55700223', activity: 'Install temporary facilities and controls', subactivity: 'ACTDUR - Actual Duration Test', workDone: '154', date: 'April 28, 2016' },
  { id: '55700223', activity: 'Install site furnishings', subactivity: 'W7245 - TW LEV3/Small Group Employment', workDone: '798', date: 'October 31, 2017' },
  { id: '52936567', activity: 'Demolish and remove existing structures', subactivity: 'W7059 - HCH 1:2/In-Home & Community Support', workDone: '826', date: 'July 14, 2015' },
  { id: '38766940', activity: 'Install site signage', subactivity: 'DEVT - Developmental Testing', workDone: '492', date: 'May 20, 2015' },
]);

const tableColumns = ref([
  { data: 'id', title: 'ID', width: 80 },
  { data: 'activity', title: 'Activity', width: 150 },
  { data: 'subactivity', title: 'Subactivity', width: 150 },
  { data: 'workDone', title: 'Work Done', width: 300 },
  { data: 'date', title: 'Date', width: 120 },
]);

// Column configuration for styling
const columnConfig = ref({});
const forceUpdate = ref(1);

// Nested headers example
const nestedHeaders = ref([
  ['Project Information', { label: 'Work Details', colspan: 2 }, 'Timeline'],
  ['ID', 'Activity', 'Subactivity', 'Work Done', 'Date'],
]);

// Row headers configuration
const rowHeadersEnabled = ref(true);
const rowHeadersConfig = ref({
  width: 60,
  title: '#',
});

// Controls
const showSkeleton = ref(false);
const eventLog = ref([]);

// Methods
function toggleSkeletonLoader() {
  showSkeleton.value = !showSkeleton.value;
  forceUpdate.value++;
  addToEventLog(`Skeleton loader ${showSkeleton.value ? 'enabled' : 'disabled'}`);
}

function changeColumnColors() {
  columnConfig.value = {
    subactivity: { backgroundColor: '#fff3e0' },
    date: { backgroundColor: '#fff3e0' },
  };
  forceUpdate.value++;
  addToEventLog('Applied column colors');
}

function resetColumnColors() {
  columnConfig.value = {};
  forceUpdate.value++;
  addToEventLog('Reset column colors');
}

function onCellClick(data) {
  addToEventLog(`Cell clicked: Row ${data.row}, Column ${data.column}`);
}

function onColumnResize(data) {
  addToEventLog(`Column ${data.column} resized to ${data.newSize}px`);
}

function onHeaderClick(data) {
  addToEventLog(`Header clicked: Column ${data.column}, Action: ${data.action}`);
}

function addToEventLog(message) {
  const timestamp = new Date().toLocaleTimeString();
  eventLog.value.unshift(`[${timestamp}] ${message}`);

  // Keep only last 10 events
  if (eventLog.value.length > 10) {
    eventLog.value = eventLog.value.slice(0, 10);
  }
}
</script>

<template>
  <div class="handsontable-example">
    <div class="controls">
      <button @click="toggleSkeletonLoader">
        {{ showSkeleton ? 'Hide' : 'Show' }} Skeleton Loader
      </button>

      <button @click="changeColumnColors">
        Apply Column Colors
      </button>

      <button @click="resetColumnColors">
        Reset Column Colors
      </button>
    </div>

    <div class="h-[calc(100%-62px)]">
      <BiHandsontable
        :key="forceUpdate"
        :column-config="columnConfig"
        :show-skeleton-loader="showSkeleton"
        :row-headers="rowHeadersEnabled"
        :row-headers-config="rowHeadersConfig"
        @cell-click="onCellClick"
        @column-resize="onColumnResize"
        @header-click="onHeaderClick"
      />
    </div>
  </div>
</template>

<style scoped lang="scss">
.handsontable-example {
  padding: 20px;

  h2 {
    margin-bottom: 20px;
    color: #333;
  }

  .controls {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;

    button {
      padding: 8px 16px;
      background: #007bff;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;

      &:hover {
        background: #0056b3;
      }
    }
  }

  .event-log {
    margin-top: 30px;

    h3 {
      margin-bottom: 10px;
      color: #333;
    }

    .log-entries {
      background: #f8f9fa;
      border: 1px solid #dee2e6;
      border-radius: 4px;
      padding: 15px;
      max-height: 200px;
      overflow-y: auto;

      .log-entry {
        padding: 4px 0;
        font-family: monospace;
        font-size: 12px;
        color: #495057;
        border-bottom: 1px solid #e9ecef;

        &:last-child {
          border-bottom: none;
        }
      }
    }
  }

  .info-section {
    margin-top: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #dee2e6;

    h3 {
      margin-bottom: 15px;
      color: #333;
      font-size: 16px;
    }

    ul {
      margin: 0;
      padding-left: 20px;

      li {
        margin-bottom: 8px;
        color: #495057;
        font-size: 14px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}
</style>
