<script setup>
defineProps({
  tables: {
    type: Object,
    default: () => ({}),
  },
});

const emit = defineEmits(['selected', 'expression']);

function onColumnsSelected(field) {
  emit('selected', field);
}

function onExpressionClicked(field) {
  emit('expression', field);
}

const is_dropdown_open = ref(false);
</script>

<template>
  <div v-click-outside="() => { is_dropdown_open = false }">
    <div @click="is_dropdown_open = true">
      <slot>
        <hawk-button>
          <IconHawkPlus />  {{ $t('Add columns') }}
        </hawk-button>
      </slot>
    </div>
    <div v-if="is_dropdown_open" class="fixed min-w-[300px] z-20">
      <bi-query-builder-columns-dropdown :tables="tables" :is-dropdown="true" :has-functions="false" @selected="onColumnsSelected" @expression="onExpressionClicked" />
    </div>
  </div>
</template>
