<script setup>
import { ref } from 'vue';

defineProps({
  primaryDataset: {
    type: Object,
    default: () => ({}),
  },
  selectedTables: {
    type: Array,
    default: () => [],
  },
  allTables: {
    type: Array,
    default: () => [],
  },
});

const is_join_dropdown_open = ref(false);
const form_data = ref(null);
</script>

<template>
  <div v-click-outside="() => is_join_dropdown_open = false">
    <div @click="is_join_dropdown_open = !is_join_dropdown_open">
      <slot>
        <hawk-button type="text" size="xs" icon>
          <IconHawkVectorJoin class="size-4" />
        </hawk-button>
      </slot>
    </div>
    <div v-if="is_join_dropdown_open" class="fixed min-w-[480px] min-h-[300px] z-20 bg-white border rounded-lg shadow-lg flex flex-col">
      <div class="w-full font-medium p-4 flex items-center justify-between border-b">
        <div class="flex items-center gap-2">
          {{ $t('Join datasets') }}
        </div>
        <div class="flex items-center gap-2" @click="is_join_dropdown_open = false">
          <hawk-button icon type="text" size="xs">
            <IconHawkXClose class="text-gray-500 size-4" />
          </hawk-button>
        </div>
      </div>
      <div class="flex-1 p-4">
        <vueform v-model="form_data" sync size="sm">
          <div class="col-span-12">
            <div>
              {{ $t('Join') }}
            </div>
            <div class="grid grid-cols-12 items-center mt-2">
              <SelectElement
                name="primary_dataset" :items="[primaryDataset]"
                :default="primaryDataset.label"
                value-prop="label"
                disabled
                object
                :columns="{
                  default: { container: 5 },
                  sm: { container: 5 },
                  md: { container: 5 },
                  lg: { container: 5 },
                }"
              />
              <div class="col-span-2 text-center">
                <hawk-button type="text" icon>
                  <IconHawkPlus class="size-3" />
                </hawk-button>
              </div>
              <SelectElement
                name="secondary_dataset"
                :items="allTables"
                value-prop="label"
                object
                :native="false"
                :columns="{
                  default: { container: 5 },
                  sm: { container: 5 },
                  md: { container: 5 },
                  lg: { container: 5 },
                }"
              />
            </div>
            <div class="mt-4">
              {{ $t('On') }}
            </div>
            <div class="grid grid-cols-12 items-center mt-2">
              {{ secondary_dataset }}
              <SelectElement
                name="primary_dataset_key"
                value-prop="label"
                :items="primaryDataset?.columns"
                :native="false"
                :columns="{
                  default: { container: 5 },
                  sm: { container: 5 },
                  md: { container: 5 },
                  lg: { container: 5 },
                }"
              />
              <div class="col-span-2 text-center">
                <hawk-button type="text" icon>
                  <IconHawkPlus class="size-3" />
                </hawk-button>
              </div>
              <SelectElement
                name="secondary_dataset_key"
                :native="false"
                value-prop="label"
                :items="form_data?.secondary_dataset?.columns || []" :columns="{
                  default: { container: 5 },
                  sm: { container: 5 },
                  md: { container: 5 },
                  lg: { container: 5 },
                }"
              />
            </div>
          </div>
        </vueform>
      </div>
      <div class="flex w-full items-center justify-between p-4 border-t">
        <div />
        <div class="flex items-center gap-2">
          <hawk-button type="outlined" @click="emit('close')">
            {{ $t('Cancel') }}
          </hawk-button>
          <hawk-button>
            {{ $t('Save') }}
          </hawk-button>
        </div>
      </div>
    </div>
  </div>
</template>
