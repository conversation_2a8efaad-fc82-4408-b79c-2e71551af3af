<script setup>
import { useBIQueryBuilder } from '~/bi/composables/useBIQueryBuilder.js';
import { useCommonImports } from '~/common/composables/common-imports.composable';

const props = defineProps({
  tables: {
    type: Array,
    default: () => [],
  },
  modelValue: {
    type: Object,
    default: () => ({}),
  },
});

const emit = defineEmits(['update:modelValue', 'fieldsDeleted', 'fieldsAdded']);

const has_row_limit = ref(false);
const is_expression_dropdown_open = ref(false);
const { getIconsForType } = useBIQueryBuilder();
const is_dropdown_open = ref(false);
const { $t } = useCommonImports();

function onColumnsSelected(field) {
  if (props.modelValue.fields.find(f => f.expression ? false : f.column.label === field.column.label && f.table.label === field.table.label && f.operator?.label === field.operator?.label))
    return;
  updateModelValue({ fields: [...props.modelValue.fields, field] });
  emit('fieldsAdded');
}

function removeField(index) {
  const field = props.modelValue.fields[index];
  // Todo: Refactor the field label logic to composable getFieldLabel
  const field_label = field.label || (field.operator ? `${field.operator.label} ${$t('of')} ${field.column.label}` : field.column.label);
  const remaining_fields = props.modelValue.fields.filter((_, i) => i !== index);
  updateModelValue({ fields: remaining_fields.filter(field => field.expression ? !field.fields.includes(field_label) : true) });
  emit('fieldsDeleted');
}

function saveExpression(expression) {
  if (props.modelValue.fields.find(f => f.expression === expression.expression))
    return;
  updateModelValue({ fields: [...props.modelValue.fields, expression] });
  is_expression_dropdown_open.value = false;
}

function onSortSelected(field) {
  if (props.modelValue.sort_fields.find(f => f.column.label === field.column.label && f.table.label === field.table.label))
    return;
  field.sort = field.sort === 'ascending' ? 'descending' : 'ascending';
  updateModelValue({ sort_fields: [...props.modelValue.sort_fields, field] });
}

function removeSortField(index) {
  updateModelValue({ sort_fields: props.modelValue.sort_fields.filter((_, i) => i !== index) });
}

function updateModelValue({ fields, sort_fields, row_limit }) {
  emit('update:modelValue', { fields: fields || props.modelValue.fields, sort_fields: sort_fields || props.modelValue.sort_fields, row_limit: has_row_limit.value ? row_limit || props.modelValue.row_limit : null });
}
</script>

<template>
  <div class="w-full">
    <div>
      <div v-for="(field, index) in modelValue.fields" :key="field.label || field.column.label" class="flex items-center justify-between gap-2 pl-6 py-1 cursor-pointer hover:bg-gray-50 rounded-lg relative group">
        <div v-if="field.expression" class="flex items-center">
          <span :class="{ 'border-l-4 border-primary-600 -ml-1': field.type === 'aggregation' }">
            <component :is="getIconsForType(field.type === 'aggregation' ? 'function' : 'formula')" class="text-gray-600 size-4 mr-2" />
          </span>
          <span class="text-sm text-gray-700">{{ field.label }}</span>
        </div>
        <div v-else class="flex items-center">
          <span :class="{ 'border-l-4 border-primary-600 -ml-1': field.operator }">
            <component :is="getIconsForType(field.operator ? 'function' : field.column.type)" class="text-gray-600 size-4 mr-2" />
          </span>
          <div v-if="field.operator" class="mr-1 text-gray-700">
            {{ field.operator.label }} {{ $t('of') }}
          </div>
          <span class="text-sm text-gray-700">{{ field.column.label }}</span>
        </div>
        <div class="text-gray-500 hidden group-hover:block mr-2" @click="removeField(index)">
          <IconHawkXClose class="size-4" />
        </div>
      </div>
      <div v-if="modelValue.sort_fields.length > 0" class="border-t p-2 mt-2">
        <div class="text-gray-600 w-full text-sm font-medium flex items-center justify-between mb-2">
          <div class="flex items-center">
            <IconHawkSwitchVerticalOne class="mr-2" />
            {{ $t('Sort') }}
          </div>
          <bi-query-builder-columns-dropdown-button :tables="tables" @selected="onSortSelected">
            <Hawk-button size="xxs" color="gray" type="light" icon>
              <IconHawkPlus class="size-3" />
            </Hawk-button>
          </bi-query-builder-columns-dropdown-button>
        </div>
        <div v-for="(field, index) in modelValue.sort_fields" :key="field.column.label" class="flex items-center justify-between gap-2 pl-6 py-1 cursor-pointer hover:bg-gray-50 rounded-lg relative group" @click="field.sort = field.sort === 'ascending' ? 'descending' : 'ascending'">
          <div class="flex items-center">
            <component :is="getIconsForType(field.sort || 'ascending')" class="text-gray-600 size-4 mr-2" />
            <span class="text-sm text-gray-700">{{ field.column.label }}</span>
          </div>
          <div class="text-gray-500 hidden group-hover:block mr-2" @click.stop="removeSortField(index)">
            <IconHawkXClose class="size-4" />
          </div>
        </div>
      </div>
      <div v-if="has_row_limit" class="mt-2 p-2 border-t">
        <div class="text-gray-600 w-full text-sm font-medium flex items-center justify-between mb-2">
          <div class="flex items-center w-[150px]">
            <IconHawkList class="mr-2" />
            {{ $t('Row Limit') }}
          </div>
          <input :value="modelValue.row_limit" type="text" placeholder="Enter row limit" class="block w-full rounded-lg border-0 py-1.5 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-500 focus:ring-1 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6 pl-4" @input="updateModelValue({ row_limit: $event.target.value })">
        </div>
      </div>
      <div v-click-outside="() => is_dropdown_open = false">
        <bi-query-builder-columns-dropdown v-if="modelValue.fields.length === 0 || is_dropdown_open" :tables="tables" @selected="onColumnsSelected" @expression="is_expression_dropdown_open = true;is_dropdown_open = false;" />
        <div v-else class="flex items-center justify-between">
          <Hawk-button type="link" @click="is_dropdown_open = true">
            <IconHawkPlus />   {{ $t('Add columns') }}
          </Hawk-button>
          <div class="flex items-center gap-2">
            <Hawk-button size="xxs" color="gray" type="light" icon>
              <IconHawkFilterFunnelOne class="size-3" />
            </Hawk-button>
            <bi-query-builder-columns-dropdown-button :tables="tables" @selected="onSortSelected">
              <Hawk-button size="xxs" color="gray" type="light" icon>
                <IconHawkSwitchVerticalOne class="size-3" />
              </Hawk-button>
            </bi-query-builder-columns-dropdown-button>
            <Hawk-button size="xxs" :color="has_row_limit ? 'primary' : 'gray'" type="light" icon @click="has_row_limit = !has_row_limit">
              <IconHawkList class="size-3" />
            </Hawk-button>
          </div>
        </div>
        <bi-query-builder-expression-editor v-if="is_expression_dropdown_open" :tables="tables" :fields="modelValue.fields" @save="saveExpression" @close="is_expression_dropdown_open = false" />
      </div>
    </div>
  </div>
</template>
