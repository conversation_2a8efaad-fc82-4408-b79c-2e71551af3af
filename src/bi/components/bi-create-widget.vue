<script setup>
const emit = defineEmits(['close']);

const state = reactive({
  mode: 'data-builder',
});

function publishWidget() {
  logger.log('PUBLISH WIDGET');
}

const columns = [{ label: 'ID', type: 'id' }, { label: 'Activity', type: 'text' }, { label: 'Subactivity', type: 'text' }, { label: 'Layer', type: 'text' }, { label: 'Sublayer', type: 'text' }, { label: 'Date', type: 'date' }, { label: 'Scope', type: 'integer' }, { label: 'Work Done', type: 'integer' }, { label: 'Temperature', type: 'float' }, { label: 'Status', type: 'text' }, { label: 'Created At', type: 'timestamp' }, { label: 'Is Active', type: 'boolean' }];
</script>

<template>
  <BiLayout @close="emit('close')">
    <template #left-content>
      <BiQueryBuilder v-if="state.mode === 'data-builder'" :selected-table="{ label: 'Progress History', columns }" />
      <BiChartBuilder v-else-if="state.mode === 'chart-builder'" @go-back="state.mode = 'data-builder'" />
    </template>
    <template #right-content>
      <BiDataPreview
        v-if="state.mode === 'data-builder'"
        @continue="state.mode = 'chart-builder'"
      />
      <BiWidgetPreview
        v-else-if="state.mode === 'chart-builder'"
        @continue="publishWidget"
      />
    </template>
  </BiLayout>
</template>
