<script setup>
const props = defineProps({
  chartType: {
    type: String,
    required: true,
  },
});
</script>

<template>
  <template v-if="props.chartType === 'bar_chart'">
    <RadiogroupElement
      view="tabs"
      name="orientation"
      label="Orientation"
      :items="{
        vertical: 'Vertical',
        horizontal: 'Horizontal',
      }"
      default="vertical"
      :native="false"
      :can-clear="false"
      :can-deselect="false"
    />
    <SelectElement
      name="data_zoom"
      label="Data zoom"
      :items="{
        disabled: 'Disabled',
        slider: 'Slider',
        inside: 'Inside',
        both: 'Both',
      }"
      default="disabled"
      :native="false"
      :can-clear="false"
      :can-deselect="false"
    />
    <ToggleElement
      name="accessibility_patterns"
      label="Accessibility patterns"
      :columns="{
        default: { container: 12, label: 11, wrapper: 12 },
        sm: { container: 12, label: 11, wrapper: 12 },
      }"
    />
    <ListElement
      name="reference_lines"
      label="Reference lines"
      add-text="Add reference line"
      :columns="{
        default: { container: 12, label: 12, wrapper: 12 },
        sm: { container: 12, label: 12, wrapper: 12 },
        md: { container: 12, label: 12, wrapper: 12 },
      }"
    >
      <!-- TODO: Incomplete -->
      <template #default="{ index }">
        <ObjectElement :name="index">
          <TextElement
            name="reference_line"
          >
            <template #addon-before>
              <div class="w-3 h-3 rounded-full bg-red-600" />
            </template>
            <template #addon-after>
              <div class="flex items-center gap-1">
                <HawkMenu position="fixed" additional_trigger_classes="p-0 m-0 mt-1.5 !ring-0 !border-0 focus:!ring-0">
                  <template #trigger>
                    <IconHawkDotsVertical class="w-4 h-4" />
                  </template>
                  <template #content>
                    <div class="p-2">
                      <TextElement
                        name="value"
                        input-type="number"
                      />
                    </div>
                  </template>
                </HawkMenu>
                <IconHawkXClose class="w-4 h-4" />
              </div>
            </template>
          </TextElement>
        </ObjectElement>
      </template>
    </ListElement>
  </template>
  <template v-else>
    Advanced - {{ props.chartType }}
  </template>
</template>
