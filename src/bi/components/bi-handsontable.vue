<script setup>
import Handsontable from 'handsontable';
// --------------------------------- Imports -------------------------------- //
import { nextTick, onMounted, ref } from 'vue';
import 'handsontable/dist/handsontable.full.min.css';

// ---------------------------------- Props --------------------------------- //
const props = defineProps({
  data: {
    type: Array,
    default: () => [],
  },
  columns: {
    type: Array,
    default: () => [],
  },
  columnConfig: {
    type: Object,
    default: () => ({}),
  },
  showSkeletonLoader: {
    type: Boolean,
    default: false,
  },
});

// ---------------------------------- Emits --------------------------------- //
const emit = defineEmits(['cell-click', 'column-resize', 'header-click']);

// ---------------------------- Injects/Provides ---------------------------- //

// ----------------------- Variables - Pinia - consts ----------------------- //

// --------------------- Variables - Pinia - storeToRefs -------------------- //

// ------------------- Variables - Local - consts and lets ------------------ //
const sampleData = [
  ['1', 'Site Survey', 'Drone Flight', 'Completed aerial mapping of solar farm section A', '2024-01-15'],
  ['2', 'Data Analysis', 'Image Processing', 'Processed 150 thermal images for hotspot detection', '2024-01-16'],
  ['3', 'Report Generation', 'Defect Analysis', 'Identified 12 potential panel defects', '2024-01-17'],
  ['4', 'Site Survey', 'Ground Inspection', 'Physical verification of flagged panels', '2024-01-18'],
  ['5', 'Data Analysis', 'Performance Analysis', 'Calculated energy loss due to defects', '2024-01-19'],
  ['6', 'Quality Control', 'Data Validation', 'Verified accuracy of thermal readings', '2024-01-20'],
  ['7', 'Report Generation', 'Client Report', 'Prepared comprehensive inspection report', '2024-01-21'],
  ['8', 'Site Survey', 'Weather Assessment', 'Documented environmental conditions', '2024-01-22'],
  ['9', 'Data Analysis', 'Trend Analysis', 'Analyzed performance trends over 6 months', '2024-01-23'],
  ['10', 'Maintenance', 'Equipment Check', 'Calibrated thermal imaging equipment', '2024-01-24'],
];

const sampleColumns = [
  { data: 0, title: 'ID', width: 80 },
  { data: 1, title: 'Activity', width: 150 },
  { data: 2, title: 'Subactivity', width: 150 },
  { data: 3, title: 'Work Done', width: 300 },
  { data: 4, title: 'Date', width: 120 },
];

// ------------------------ Variables - Local - refs ------------------------ //
const tableContainer = ref(null);
const hotInstance = ref(null);

// ---------------------- Variables - Local - reactives --------------------- //

// --------------------------- Computed properties -------------------------- //

// -------------------------------- Functions ------------------------------- //
function customRenderer(instance, td, row, col, prop, value, cellProperties) {
  // Apply skeleton loader if needed
  if (props.showSkeletonLoader) {
    td.innerHTML = '<div class="skeleton-loader"></div>';
    td.className = 'skeleton-cell';
    return td;
  }

  // Default text renderer
  Handsontable.renderers.TextRenderer.apply(this, arguments);

  // Apply column background color if configured
  const columnKey = sampleColumns[col]?.title || col;
  const columnColor = props.columnConfig[columnKey]?.backgroundColor;

  if (columnColor) {
    td.style.backgroundColor = columnColor;
  }

  // Make cells readonly
  cellProperties.readOnly = true;

  return td;
}

function createHeaderRenderer(col) {
  return function (col, TH) {
    const columnKey = sampleColumns[col]?.title || col;
    const originalTitle = sampleColumns[col]?.title || `Column ${col + 1}`;

    TH.innerHTML = `
      <div class="custom-header">
        <span class="header-title">${originalTitle}</span>
        <div class="header-icons">
          <i class="icon-settings" title="Settings"></i>
          <i class="icon-filter" title="Filter"></i>
          <i class="icon-sort" title="Sort"></i>
        </div>
      </div>
    `;

    // Add hover event listeners
    TH.addEventListener('mouseenter', () => {
      const icons = TH.querySelector('.header-icons');
      if (icons)
        icons.style.opacity = '1';
    });

    TH.addEventListener('mouseleave', () => {
      const icons = TH.querySelector('.header-icons');
      if (icons)
        icons.style.opacity = '0';
    });

    // Add click event for header icons
    const icons = TH.querySelectorAll('.header-icons i');
    icons.forEach((icon) => {
      icon.addEventListener('click', (e) => {
        e.stopPropagation();
        emit('header-click', { column: col, action: icon.className });
      });
    });
  };
}

function initializeTable() {
  if (!tableContainer.value)
    return;

  const settings = {
    data: props.data.length > 0 ? props.data : sampleData,
    columns: props.columns.length > 0 ? props.columns : sampleColumns,
    colHeaders: true,
    rowHeaders: true,
    manualColumnResize: true,
    autoColumnSize: false,
    licenseKey: 'non-commercial-and-evaluation',

    // Nested headers support
    nestedHeaders: [
      ['Project Details', { label: 'Work Information', colspan: 2 }, 'Timeline'],
      ['ID', 'Activity', 'Subactivity', 'Work Done', 'Date'],
    ],

    // Custom cell renderer
    cells(row, col) {
      return {
        renderer: customRenderer,
      };
    },

    // Column header renderer
    afterGetColHeader(col, TH) {
      createHeaderRenderer(col)(col, TH);
    },

    // Double click to autofit column
    afterOnCellMouseDown(event, coords, TD) {
      if (coords.row === -1 && event.detail === 2) { // Double click on header
        this.getPlugin('autoColumnSize').calculateColumnsWidth(coords.col, coords.col, true);
      }
    },

    // Column resize callback
    afterColumnResize(newSize, column, isDoubleClick) {
      emit('column-resize', { column, newSize, isDoubleClick });
    },

    // Cell click callback
    afterSelectionEnd(row, column, row2, column2) {
      emit('cell-click', { row, column, row2, column2 });
    },
  };

  hotInstance.value = new Handsontable(tableContainer.value, settings);
}

function refreshTable() {
  if (hotInstance.value) {
    hotInstance.value.render();
  }
}

// -------------------------------- Watchers -------------------------------- //

// ----------------------------- Lifecycle Hooks ---------------------------- //
onMounted(async () => {
  await nextTick();
  initializeTable();
});
</script>

<template>
  <div class="handsontable-wrapper">
    <!-- CDN for Handsontable theme -->
    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/handsontable@15.3/styles/ht-theme-main.css"
    >

    <div
      ref="tableContainer"
      class="handsontable-container"
    />
  </div>
</template>

<style scoped lang="scss">
.handsontable-wrapper {
  width: 100%;
  height: 100%;

  .handsontable-container {
    width: 100%;
    height: 500px; // Adjust as needed
  }
}

// Custom header styles
:deep(.custom-header) {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;

  .header-title {
    font-weight: 600;
    color: #333;
  }

  .header-icons {
    display: flex;
    gap: 4px;
    opacity: 0;
    transition: opacity 0.2s ease;

    i {
      cursor: pointer;
      padding: 2px;
      border-radius: 2px;
      font-size: 12px;
      color: #666;

      &:hover {
        background-color: rgba(0, 0, 0, 0.1);
        color: #333;
      }

      &.icon-settings::before {
        content: '⚙️';
      }

      &.icon-filter::before {
        content: '🔍';
      }

      &.icon-sort::before {
        content: '↕️';
      }
    }
  }
}

// Skeleton loader styles
:deep(.skeleton-loader) {
  width: 100%;
  height: 16px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4px;
}

:deep(.skeleton-cell) {
  padding: 8px !important;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

// Override Handsontable styles for better integration
:deep(.handsontable) {
  font-family: inherit;

  .ht_master .wtHolder {
    border: 1px solid #e0e0e0;
    border-radius: 4px;
  }

  .colHeader {
    background: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
  }

  .rowHeader {
    background: #f8f9fa;
    border-right: 2px solid #dee2e6;
  }

  td {
    border-color: #e9ecef;
  }

  .area {
    background: rgba(74, 144, 226, 0.1);
  }
}
</style>
