<script setup>
// --------------------------------- Imports -------------------------------- //
import { nextTick, onMounted, ref } from 'vue';
import { load_js_css_file } from '~/common/utils/load-script.util';

// ---------------------------------- Props --------------------------------- //
const props = defineProps({
  biTableId: {
    type: String,
    default: 'bi-handsontable',
  },
  height: {
    type: String,
    default: '600px',
  },
  width: {
    type: String,
    default: '100%',
  },
  data: {
    type: Array,
    default: () => [],
  },
  columns: {
    type: Array,
    default: () => [],
  },
  columnConfig: {
    type: Object,
    default: () => ({}),
  },
  nestedHeaders: {
    type: Array,
    default: () => [],
  },
  showSkeletonLoader: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['columnResize', 'cellClick', 'headerClick']);

const sampleData = [
  { id: '1', activity: 'Site Survey', subactivity: 'Drone Flight', workDone: 'Completed aerial mapping of solar farm section A', date: '2024-01-15' },
  { id: '2', activity: 'Data Analysis', subactivity: 'Image Processing', workDone: 'Processed 150 thermal images for hotspot detection', date: '2024-01-16' },
  { id: '3', activity: 'Report Generation', subactivity: 'Defect Analysis', workDone: 'Identified 12 potential panel defects', date: '2024-01-17' },
  { id: '4', activity: 'Site Survey', subactivity: 'Ground Inspection', workDone: 'Physical verification of flagged panels', date: '2024-01-18' },
  { id: '5', activity: 'Data Analysis', subactivity: 'Performance Analysis', workDone: 'Calculated energy loss due to defects', date: '2024-01-19' },
  { id: '6', activity: 'Quality Control', subactivity: 'Data Validation', workDone: 'Verified accuracy of thermal readings', date: '2024-01-20' },
  { id: '7', activity: 'Report Generation', subactivity: 'Client Report', workDone: 'Prepared comprehensive inspection report', date: '2024-01-21' },
  { id: '8', activity: 'Site Survey', subactivity: 'Weather Assessment', workDone: 'Documented environmental conditions', date: '2024-01-22' },
  { id: '9', activity: 'Data Analysis', subactivity: 'Trend Analysis', workDone: 'Analyzed performance trends over 6 months', date: '2024-01-23' },
  { id: '10', activity: 'Maintenance', subactivity: 'Equipment Check', workDone: 'Calibrated thermal imaging equipment', date: '2024-01-24' },
];

const sampleColumns = [
  { data: 'id', title: 'ID', width: 80 },
  { data: 'activity', title: 'Activity', width: 150 },
  { data: 'subactivity', title: 'Subactivity', width: 150 },
  { data: 'workDone', title: 'Work Done', width: 300 },
  { data: 'date', title: 'Date', width: 120 },
];

const hotInstance = ref(null);
const isLoading = ref(false);

async function loadHandsontableFiles() {
  try {
    isLoading.value = true;

    // Load Handsontable CSS
    await load_js_css_file(
      'https://cdn.jsdelivr.net/npm/handsontable@15.3.0/dist/handsontable.full.min.css',
      'handsontable-new-css',
      'css',
    );

    // Load Handsontable theme CSS
    await load_js_css_file(
      'https://cdn.jsdelivr.net/npm/handsontable@15.3/styles/ht-theme-main.css',
      'handsontable-theme-css',
      'css',
    );

    // Load Handsontable JS
    await load_js_css_file(
      'https://cdn.jsdelivr.net/npm/handsontable@15.3.0/dist/handsontable.full.min.js',
      'handsontable-new-js',
      'js',
    );

    isLoading.value = false;
  }
  catch (error) {
    console.error('Error loading Handsontable files:', error);
    isLoading.value = false;
  }
}
function customRenderer(instance, td, row, col, prop, value, cellProperties) {
  // Apply skeleton loader if needed
  if (props.showSkeletonLoader) {
    td.innerHTML = '<div class="skeleton-loader"></div>';
    td.className = 'skeleton-cell';
    return td;
  }

  // Default text renderer - use window.Handsontable when loaded dynamically
  if (window.Handsontable) {
    // eslint-disable-next-line prefer-rest-params
    window.Handsontable.renderers.TextRenderer.apply(this, arguments);
  }
  else {
    // Fallback if Handsontable is not loaded yet
    td.innerHTML = value || '';
  }

  // Apply column background color if configured
  const columnKey = sampleColumns[col]?.data || sampleColumns[col]?.title || col;
  const columnColor = props.columnConfig[columnKey]?.backgroundColor;

  if (columnColor) {
    td.style.backgroundColor = columnColor;
  }

  // Make cells readonly
  cellProperties.readOnly = true;

  return td;
}

function createHeaderRenderer() {
  return function (col, TH) {
    const columnData = sampleColumns[col];
    const originalTitle = columnData?.title || `Column ${col + 1}`;

    TH.innerHTML = `
      <div class="custom-header">
        <span class="header-title">${originalTitle}</span>
        <div class="header-icons">
          <i class="icon-settings" title="Settings"></i>
          <i class="icon-filter" title="Filter"></i>
          <i class="icon-sort" title="Sort"></i>
        </div>
      </div>
    `;

    // Add hover event listeners
    TH.addEventListener('mouseenter', () => {
      const icons = TH.querySelector('.header-icons');
      if (icons)
        icons.style.opacity = '1';
    });

    TH.addEventListener('mouseleave', () => {
      const icons = TH.querySelector('.header-icons');
      if (icons)
        icons.style.opacity = '0';
    });

    // Add click event for header icons
    const icons = TH.querySelectorAll('.header-icons i');
    icons.forEach((icon) => {
      icon.addEventListener('click', (e) => {
        e.stopPropagation();
        emit('headerClick', { column: col, action: icon.className });
      });
    });
  };
}

function initializeTable() {
  if (!window.Handsontable)
    return;

  const settings = {
    data: props.data.length > 0 ? props.data : sampleData,
    columns: props.columns.length > 0 ? props.columns : sampleColumns,
    manualColumnResize: true,
    autoColumnSize: false,
    height: props.height,
    width: props.width,

    licenseKey: 'non-commercial-and-evaluation',
    viewportRowRenderingOffset: 100,

    // Nested headers support
    ...(props.nestedHeaders?.length && { nestedHeaders: props.nestedHeaders }),

    // Custom cell renderer
    cells() {
      return {
        renderer: customRenderer,
      };
    },

    // Column header renderer
    afterGetColHeader(col, TH) {
      createHeaderRenderer(col)(col, TH);
    },

    // Double click to autofit column
    afterOnCellMouseDown(event, coords) {
      if (coords.row === -1 && event.detail === 2) { // Double click on header
        this.getPlugin('autoColumnSize').calculateColumnsWidth(coords.col, coords.col, true);
      }
    },

    // Column resize callback
    afterColumnResize(newSize, column, isDoubleClick) {
      emit('columnResize', { column, newSize, isDoubleClick });
    },

    // Cell click callback
    afterSelectionEnd(row, column, row2, column2) {
      emit('cellClick', { row, column, row2, column2 });
    },
  };
  const container = document.getElementById(props.biTableId);

  hotInstance.value = new window.Handsontable(container, settings);
}

onMounted(async () => {
  isLoading.value = true;
  await loadHandsontableFiles();
  initializeTable();
  isLoading.value = false;
});
</script>

<template>
  <HawkLoader v-if="isLoading" />
  <div :id="biTableId" class="ht-theme-main" />
</template>

<style scoped lang="scss">
// Skeleton loader styles
:deep(.skeleton-loader) {
  width: 100%;
  height: 16px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4px;
}

:deep(.skeleton-cell) {
  padding: 8px !important;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}
</style>
