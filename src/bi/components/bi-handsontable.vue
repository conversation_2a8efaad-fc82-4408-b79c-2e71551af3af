<script setup>
// --------------------------------- Imports -------------------------------- //
import { nextTick, onMounted, ref } from 'vue';
import { load_js_css_file } from '~/common/utils/load-script.util';

// ---------------------------------- Props --------------------------------- //
const props = defineProps({
  biTableId: {
    type: String,
    default: 'bi-handsontable',
  },
  height: {
    type: String,
    default: '600px',
  },
  width: {
    type: String,
    default: '100%',
  },
  data: {
    type: Array,
    default: () => [],
  },
  columns: {
    type: Array,
    default: () => [],
  },
  columnConfig: {
    type: Object,
    default: () => ({}),
  },
  nestedHeaders: {
    type: Array,
    default: () => [],
  },
  showSkeletonLoader: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['columnResize', 'cellClick', 'headerClick']);

const sampleData = [
  { id: '66277431', activity: 'Install formwork and reinforcing for site paving', subactivity: 'EXPL - Assessment', workDone: '647', date: 'August 7, 2017' },
  { id: '55700223', activity: 'Install temporary facilities and controls', subactivity: 'ACTDUR - Actual Duration Test', workDone: '154', date: 'April 28, 2016' },
  { id: '55700223', activity: 'Install site furnishings', subactivity: 'W7245 - TW LEV3/Small Group Employment', workDone: '798', date: 'October 31, 2017' },
  { id: '52936567', activity: 'Demolish and remove existing structures', subactivity: 'W7059 - HCH 1:2/In-Home & Community Support', workDone: '826', date: 'July 14, 2015' },
  { id: '38766940', activity: 'Install site signage', subactivity: 'DEVT - Developmental Testing', workDone: '492', date: 'May 20, 2015' },
  { id: '11081197', activity: 'Install temporary facilities and controls', subactivity: 'W2025 - Supported Employment – Job Support', workDone: '196', date: 'November 16, 2014' },
  { id: '66538135', activity: 'Install site lighting and electrical', subactivity: 'W5996 - 100% Community 1:1', workDone: '703', date: 'October 24, 2018' },
  { id: '66538135', activity: 'Backfill foundation and footing trenches', subactivity: 'W1726 - Companion', workDone: '540', date: 'May 6, 2012' },
  { id: '01906912', activity: 'Excavate and prepare building foundation', subactivity: 'W1726 - Companion', workDone: '877', date: 'September 24, 2017' },
  { id: '76031847', activity: 'Place and finish site paving', subactivity: 'W9794 - Job Support', workDone: '922', date: 'May 31, 2015' },
  { id: '37890606', activity: 'Place and finish site paving', subactivity: 'W2025 - Supported Employment – Job Support', workDone: '536', date: 'February 9, 2015' },
  { id: '58276066', activity: 'Install underground utilities', subactivity: 'W7059 - HCH 1:2/In-Home & Community Support', workDone: '423', date: 'August 2, 2013' },
  { id: '01906912', activity: 'Mobilize construction equipment and personnel', subactivity: 'W7059 - HCH 1:2/In-Home & Community Support', workDone: '600', date: 'December 29, 2012' },
  { id: '01906912', activity: 'Place and finish site paving', subactivity: 'W7275 - Transportation Zone 2', workDone: '357', date: 'November 28, 2015' },
  { id: '37890606', activity: 'Install site lighting and electrical', subactivity: 'W7275 - Transportation Zone 2', workDone: '130', date: 'May 12, 2019' },
  { id: '37890606', activity: 'Install temporary facilities and controls', subactivity: 'SUMR - Summer Program', workDone: '883', date: 'March 6, 2018' },
  { id: '37890606', activity: 'Install site curbs and gutters', subactivity: 'W7059 - HCH 1:2/In-Home & Community Support', workDone: '185', date: 'May 9, 2014' },
  { id: '93242854', activity: 'Install site landscaping and irrigation', subactivity: 'W7059 - HCH 1:2/In-Home & Community Support', workDone: '426', date: 'February 29, 2012' },
  { id: '29103050', activity: 'Perform site clean-up and demobilization', subactivity: 'W2025 - Supported Employment – Job Support', workDone: '561', date: 'May 29, 2017' },
  { id: '93242854', activity: 'Install subgrade and base course for site paving', subactivity: 'SUMR - Summer Program', workDone: '740', date: 'October 25, 2019' },
  { id: '93242854', activity: 'Excavate and prepare building foundation', subactivity: 'SICK - Sick Day', workDone: '447', date: 'October 30, 2017' },
  { id: '34034474', activity: 'Install underground utilities', subactivity: 'W1726 - Companion', workDone: '994', date: 'March 23, 2013' },
  { id: '58276066', activity: 'Perform site clean-up and demobilization', subactivity: 'EXPL - Assessment', workDone: '453', date: 'February 11, 2014' },
  { id: '55069827', activity: 'Install site fencing and gates', subactivity: 'SICK - Sick Day', workDone: '816', date: 'February 28, 2018' },
  { id: '43397744', activity: 'Install site fencing and gates', subactivity: 'SICK - Sick Day', workDone: '583', date: 'February 28, 2018' },
  { id: '43397744', activity: 'Install site fencing and gates', subactivity: 'SICK - Sick Day', workDone: '429', date: 'February 28, 2018' },
];

const sampleColumns = [
  { data: 'id', title: 'ID', width: 80 },
  { data: 'activity', title: 'Activity', width: 150 },
  { data: 'subactivity', title: 'Subactivity', width: 150 },
  { data: 'workDone', title: 'Work Done', width: 300 },
  { data: 'date', title: 'Date', width: 120 },
];

const hotInstance = ref(null);
const isLoading = ref(false);

async function loadHandsontableFiles() {
  try {
    isLoading.value = true;

    // Load Handsontable CSS
    await load_js_css_file(
      'https://cdn.jsdelivr.net/npm/handsontable@15.3.0/dist/handsontable.full.min.css',
      'handsontable-new-css',
      'css',
    );

    // Load Handsontable theme CSS
    await load_js_css_file(
      'https://cdn.jsdelivr.net/npm/handsontable@15.3/styles/ht-theme-main.css',
      'handsontable-theme-css',
      'css',
    );

    // Load Handsontable JS
    await load_js_css_file(
      'https://cdn.jsdelivr.net/npm/handsontable@15.3.0/dist/handsontable.full.min.js',
      'handsontable-new-js',
      'js',
    );

    isLoading.value = false;
  }
  catch (error) {
    console.error('Error loading Handsontable files:', error);
    isLoading.value = false;
  }
}
function customRenderer(instance, td, row, col, prop, value, cellProperties) {
  // Apply skeleton loader if needed
  if (props.showSkeletonLoader) {
    td.innerHTML = '<div class="skeleton-loader"></div>';
    td.className = 'skeleton-cell';
    return td;
  }

  // Default text renderer - use window.Handsontable when loaded dynamically
  if (window.Handsontable) {
    // eslint-disable-next-line prefer-rest-params
    window.Handsontable.renderers.TextRenderer.apply(this, arguments);
  }
  else {
    // Fallback if Handsontable is not loaded yet
    td.innerHTML = value || '';
  }

  // Add padding using inline styles (not CSS classes)
  td.style.padding = '12px 16px';
  td.style.textAlign = 'left';
  td.style.verticalAlign = 'middle';
  td.style.fontSize = '14px';
  td.style.lineHeight = '1.4';

  // Apply column background color if configured
  const columnKey = sampleColumns[col]?.data || sampleColumns[col]?.title || col;
  const columnColor = props.columnConfig[columnKey]?.backgroundColor;

  if (columnColor) {
    td.style.backgroundColor = columnColor;
  }

  // Make cells readonly
  cellProperties.readOnly = true;

  return td;
}

function createHeaderRenderer() {
  return function (col, TH) {
    const columnData = sampleColumns[col];
    const originalTitle = columnData?.title || `Column ${col + 1}`;

    // Add padding and styling directly to the header
    TH.style.padding = '12px 16px';
    TH.style.textAlign = 'left';
    TH.style.fontWeight = '600';
    TH.style.fontSize = '14px';
    TH.style.backgroundColor = '#f8f9fa';
    TH.style.color = '#495057';

    TH.innerHTML = `
      <div style="display: flex; align-items: center; justify-content: space-between;">
        <span>${originalTitle}</span>
        <div class="header-icons" style="opacity: 0; display: flex; gap: 8px; transition: opacity 0.2s;">
          <span style="cursor: pointer; padding: 4px;">⚙️</span>
          <span style="cursor: pointer; padding: 4px;">🔍</span>
          <span style="cursor: pointer; padding: 4px;">↕️</span>
        </div>
      </div>
    `;

    // Add hover event listeners
    TH.addEventListener('mouseenter', () => {
      const icons = TH.querySelector('.header-icons');
      if (icons)
        icons.style.opacity = '1';
    });

    TH.addEventListener('mouseleave', () => {
      const icons = TH.querySelector('.header-icons');
      if (icons)
        icons.style.opacity = '0';
    });

    // Add click event for header icons
    const icons = TH.querySelectorAll('.header-icons span');
    icons.forEach((icon, index) => {
      icon.addEventListener('click', (e) => {
        e.stopPropagation();
        const actions = ['settings', 'filter', 'sort'];
        emit('headerClick', { column: col, action: actions[index] });
      });
    });
  };
}

function initializeTable() {
  if (!window.Handsontable)
    return;

  const settings = {
    data: props.data.length > 0 ? props.data : sampleData,
    columns: props.columns.length > 0 ? props.columns : sampleColumns,
    manualColumnResize: true,
    autoColumnSize: false,
    height: props.height,
    width: props.width,

    licenseKey: 'non-commercial-and-evaluation',
    viewportRowRenderingOffset: 100,

    // Headers
    colHeaders: true,
    rowHeaders: false,

    // Table styling for clean look
    className: 'htLeft htMiddle',

    // Cell padding and spacing
    cellPadding: 12,
    rowHeaderWidth: 0,

    // Disable borders
    customBorders: false,

    // Nested headers support
    ...(props.nestedHeaders?.length && { nestedHeaders: props.nestedHeaders }),

    // Custom cell renderer
    cells() {
      return {
        renderer: customRenderer,
      };
    },

    // Column header renderer
    afterGetColHeader(col, TH) {
      createHeaderRenderer(col)(col, TH);
    },

    // Double click to autofit column
    afterOnCellMouseDown(event, coords) {
      if (coords.row === -1 && event.detail === 2) { // Double click on header
        this.getPlugin('autoColumnSize').calculateColumnsWidth(coords.col, coords.col, true);
      }
    },

    // Column resize callback
    afterColumnResize(newSize, column, isDoubleClick) {
      emit('columnResize', { column, newSize, isDoubleClick });
    },

    // Cell click callback
    afterSelectionEnd(row, column, row2, column2) {
      emit('cellClick', { row, column, row2, column2 });
    },
  };
  const container = document.getElementById(props.biTableId);

  hotInstance.value = new window.Handsontable(container, settings);
}

onMounted(async () => {
  isLoading.value = true;
  await loadHandsontableFiles();
  initializeTable();
  isLoading.value = false;
});
</script>

<template>
  <HawkLoader v-if="isLoading" />
  <div :id="biTableId" class="ht-theme-main" />
</template>

<style scoped lang="scss">
// Skeleton loader styles
:deep(.skeleton-loader) {
  width: 100%;
  height: 16px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4px;
}

:deep(.skeleton-cell) {
  padding: 8px !important;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}
</style>
