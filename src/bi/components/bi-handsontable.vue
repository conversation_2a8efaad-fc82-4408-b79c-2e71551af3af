<script setup>
// --------------------------------- Imports -------------------------------- //
import { nextTick, onMounted, ref } from 'vue';
import { load_js_css_file } from '~/common/utils/load-script.util';

// ---------------------------------- Props --------------------------------- //
const props = defineProps({
  biTableId: {
    type: String,
    default: 'bi-handsontable',
  },
  height: {
    type: String,
    default: '600px',
  },
  width: {
    type: String,
    default: '100%',
  },
  data: {
    type: Array,
    default: () => [],
  },
  columns: {
    type: Array,
    default: () => [],
  },
  columnConfig: {
    type: Object,
    default: () => ({}),
  },
  nestedHeaders: {
    type: Array,
    default: () => [],
  },
  showSkeletonLoader: {
    type: Boolean,
    default: false,
  },
  sortConfig: {
    type: Array,
    default: () => ([]),
  },
  rowHeaders: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['columnResize', 'filterClicked', 'columnSort']);

const hotInstance = ref(null);
const isLoading = ref(false);

async function loadHandsontableFiles() {
  try {
    isLoading.value = true;

    // Load Handsontable CSS
    await load_js_css_file(
      'https://cdn.jsdelivr.net/npm/handsontable@15.3/styles/handsontable.min.css',
      'handsontable-css',
      'css',
    );

    // Load Handsontable theme CSS
    await load_js_css_file(
      'https://cdn.jsdelivr.net/npm/handsontable@15.3/styles/ht-theme-main.css',
      'handsontable-theme-css',
      'css',
    );

    isLoading.value = false;
  }
  catch (error) {
    console.error('Error loading Handsontable files:', error);
    isLoading.value = false;
  }
}
function customRenderer(instance, td, row, col, prop, value, cellProperties) {
  // Apply skeleton loader if needed
  if (props.showSkeletonLoader) {
    td.innerHTML = '<div class="skeleton-loader"></div>';
    td.className = 'skeleton-cell';
    return td;
  }

  // Default text renderer - use window.Handsontable when loaded dynamically
  if (window.Handsontable) {
    // eslint-disable-next-line prefer-rest-params
    window.Handsontable.renderers.TextRenderer.apply(this, arguments);
  }
  else {
    // Fallback if Handsontable is not loaded yet
    td.innerHTML = value || '';
  }

  // Add padding using inline styles (not CSS classes)
  td.style.padding = '8px 16px';
  td.style.textAlign = 'left';
  td.style.verticalAlign = 'middle';
  td.style.fontSize = '14px';
  td.style.lineHeight = '1.4';

  // Apply column background color if configured
  const columnKey = props.columns[col]?.data || props.columns[col]?.title || col;
  const columnColor = props.columnConfig[columnKey]?.backgroundColor;

  if (columnColor) {
    td.style.backgroundColor = columnColor;
  }

  // Make cells readonly
  cellProperties.readOnly = true;

  return td;
}
customRenderer.fastRenderer = true;

function createHeaderRenderer() {
  return function (col, TH) {
    // Check if this is a leaf level header (actual column header, not nested group header)
    const thead = TH.closest('thead');
    const isNestedHeader = thead?.querySelectorAll('tr').length > 1;
    const currentRow = TH.closest('tr');
    const headerLevel = Array.from(thead.querySelectorAll('tr')).indexOf(currentRow);
    const hasColspan = TH.getAttribute('colspan') && Number.parseInt(TH.getAttribute('colspan')) > 1;

    // Only apply custom rendering to leaf level headers (bottom row in nested structure or single row)
    const isLeafHeader = !hasColspan && (!isNestedHeader || headerLevel === thead.querySelectorAll('tr').length - 1);

    const div = TH.querySelector('div[role="presentation"]');
    div.style.padding = '0px';

    if (!isLeafHeader) {
      TH.style.padding = '8px 12px';
      return;
    }
    TH.classList.add('group');
    TH.style.padding = '8px 0px';
    if (col === -1)
      return;

    TH.style.textAlign = 'left';
    const columnData = props.columns[col];
    const columnKey = columnData?.data || columnData?.title || col;
    const columnColor = props.columnConfig[columnKey]?.backgroundColor;

    // Add padding and styling directly to the header
    div.style.display = 'flex';
    div.style.alignItems = 'center';
    div.style.justifyContent = 'space-between';

    const filterSpan = div.querySelector('#filter-span');

    if (!filterSpan) {
    // Step 3: Create and style the span
      const span = document.createElement('span');
      span.id = 'filter-span';
      span.style.marginLeft = 'auto';
      span.style.cursor = 'pointer';

      span.innerHTML = `
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M2 4.6C2 4.03995 2 3.75992 2.10899 3.54601C2.20487 3.35785 2.35785 3.20487 2.54601 3.10899C2.75992 3 3.03995 3 3.6 3H20.4C20.9601 3 21.2401 3 21.454 3.10899C21.6422 3.20487 21.7951 3.35785 21.891 3.54601C22 3.75992 22 4.03995 22 4.6V5.26939C22 5.53819 22 5.67259 21.9672 5.79756C21.938 5.90831 21.8901 6.01323 21.8255 6.10776C21.7526 6.21443 21.651 6.30245 21.4479 6.4785L15.0521 12.0215C14.849 12.1975 14.7474 12.2856 14.6745 12.3922C14.6099 12.4868 14.562 12.5917 14.5328 12.7024C14.5 12.8274 14.5 12.9618 14.5 13.2306V18.4584C14.5 18.6539 14.5 18.7517 14.4685 18.8363C14.4406 18.911 14.3953 18.9779 14.3363 19.0315C14.2695 19.0922 14.1787 19.1285 13.9971 19.2012L10.5971 20.5612C10.2296 20.7082 10.0458 20.7817 9.89827 20.751C9.76927 20.7242 9.65605 20.6476 9.58325 20.5377C9.5 20.4122 9.5 20.2142 9.5 19.8184V13.2306C9.5 12.9618 9.5 12.8274 9.46715 12.7024C9.43805 12.5917 9.39014 12.4868 9.32551 12.3922C9.25258 12.2856 9.15102 12.1975 8.94789 12.0215L2.55211 6.4785C2.34898 6.30245 2.24742 6.21443 2.17449 6.10776C2.10986 6.01323 2.06195 5.90831 2.03285 5.79756C2 5.67259 2 5.53819 2 5.26939V4.6Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      `;

      span.classList.add('opacity-0', 'group-hover:opacity-100', 'mr-3');

      span.addEventListener('click', (event) => {
        emit('filterClicked', { column: columnData, event });
      });

      div.appendChild(span);
    }

    if (columnColor) {
      TH.style.backgroundColor = columnColor;
    }
  };
}

function afterColumnResize() {
  const columnWidths = [];
  for (let i = 0; i < hotInstance.value.countCols(); i++) {
    columnWidths.push(hotInstance.value.getColWidth(i));
  }
  emit('columnResize', columnWidths);
}

function afterColumnSort(_, destinationSortConfigs) {
  const sorted_columns = destinationSortConfigs.map((config) => {
    const column_details = props.columns[config.column];
    return {
      column: column_details?.data || column_details?.title,
      sortOrder: config.sortOrder,
    };
  });
  emit('columnSort', sorted_columns);
}

function initializeTable() {
  if (!window.BiHandsontable)
    return;

  const defaultCellProps = { renderer: customRenderer };
  const headerRenderersCache = {};

  const settings = {
    data: props.data,
    columns: props.columns,
    autoColumnSize: false,
    height: props.height,
    width: props.width,

    licenseKey: 'non-commercial-and-evaluation',

    multiColumnSorting: {
      initialConfig: (props.sortConfig || []).map(config => ({
        column: config.column || props.columns.findIndex(column => column.data === config.key),
        sortOrder: config.sortOrder,
      })),
    },

    // Virtualization tuning
    viewportRowRenderingOffset: 100,
    viewportColumnRenderingOffset: 20,
    renderAllRows: false,
    renderOnlyVisibleRows: true,

    // Headers
    colHeaders: true,
    rowHeaders: props.rowHeaders,
    manualColumnResize: true,
    themeName: 'ht-theme-main',

    // Key setting to stretch columns
    stretchH: 'all',

    // Table styling for clean look
    className: 'htLeft htMiddle',

    // Cell padding and spacing
    cellPadding: 12,

    // Disable borders
    customBorders: false,

    // Nested headers support
    ...(props.nestedHeaders?.length && { nestedHeaders: props.nestedHeaders, collapsibleColumns: true }),

    // Custom cell renderer
    cells() {
      return defaultCellProps;
    },

    // Column header renderer
    afterGetColHeader(col, TH) {
      if (!headerRenderersCache[col]) {
        headerRenderersCache[col] = createHeaderRenderer(col);
      }
      headerRenderersCache[col](col, TH);
    },

    // Column resize callback
    afterColumnResize,

    // Column sort callback
    afterColumnSort,
  };
  const container = document.getElementById(props.biTableId);

  hotInstance.value = new window.BiHandsontable(container, settings);
  isLoading.value = false;
}

onMounted(async () => {
  isLoading.value = true;
  await loadHandsontableFiles();
  import('handsontable').then((module) => {
    if (!window.BiHandsontable) {
      window.BiHandsontable = module.default;
      initializeTable();
    }
    else {
      initializeTable();
    }
  });
});

onUnmounted(() => {
  if (hotInstance.value)
    hotInstance.value.destroy();
  window.BiHandsontable = null;
});
</script>

<template>
  <HawkLoader v-if="isLoading" />
  <div class="bi-handsontable-wrapper">
    <div :id="biTableId" class="ht-theme-main" />
  </div>
</template>

<style scoped lang="scss">
.bi-handsontable-wrapper {
  &:deep(.handsontable tr td:first-child) {
    border-left: 1px solid #e0e0e0 !important;
  }
  &:deep(.handsontable th.ht__active_highlight) {
    background-color: rgb(26, 66, 232) !important;
  }

  // Simple row hover highlighting with CSS only
  &:deep(.handsontable tbody tr:hover td) {
    background-color: #EEF4F9 !important;
    transition: background-color 0.15s ease;
  }

  &:deep(.handsontable) {
    &.ht_clone_top, &.ht_clone_top_left_corner, &.ht_clone_left, &.ht_clone_inline_start {
      z-index: 1;
    }
  }
}

// Skeleton loader styles
:deep(.skeleton-loader) {
  width: 100%;
  height: 16px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4px;
}

:deep(.skeleton-cell) {
  padding: 8px !important;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}
</style>
