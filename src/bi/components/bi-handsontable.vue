<script setup>
// --------------------------------- Imports -------------------------------- //
import { nextTick, onMounted, ref } from 'vue';
import { load_js_css_file } from '~/common/utils/load-script.util';

// ---------------------------------- Props --------------------------------- //
const props = defineProps({
  biTableId: {
    type: String,
    default: 'bi-handsontable',
  },
  height: {
    type: String,
    default: '600px',
  },
  width: {
    type: String,
    default: '100%',
  },
  data: {
    type: Array,
    default: () => [],
  },
  columns: {
    type: Array,
    default: () => [],
  },
  columnConfig: {
    type: Object,
    default: () => ({}),
  },
  nestedHeaders: {
    type: Array,
    default: () => [],
  },
  showSkeletonLoader: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['columnResize', 'cellClick', 'headerClick']);

const sampleData = [
  { id: '66277431', activity: 'Install formwork and reinforcing for site paving', subactivity: 'EXPL - Assessment', workDone: '647', date: 'August 7, 2017' },
  { id: '55700223', activity: 'Install temporary facilities and controls', subactivity: 'ACTDUR - Actual Duration Test', workDone: '154', date: 'April 28, 2016' },
  { id: '55700223', activity: 'Install site furnishings', subactivity: 'W7245 - TW LEV3/Small Group Employment', workDone: '798', date: 'October 31, 2017' },
  { id: '52936567', activity: 'Demolish and remove existing structures', subactivity: 'W7059 - HCH 1:2/In-Home & Community Support', workDone: '826', date: 'July 14, 2015' },
  { id: '38766940', activity: 'Install site signage', subactivity: 'DEVT - Developmental Testing', workDone: '492', date: 'May 20, 2015' },
  { id: '11081197', activity: 'Install temporary facilities and controls', subactivity: 'W2025 - Supported Employment – Job Support', workDone: '196', date: 'November 16, 2014' },
  { id: '66538135', activity: 'Install site lighting and electrical', subactivity: 'W5996 - 100% Community 1:1', workDone: '703', date: 'October 24, 2018' },
  { id: '66538135', activity: 'Backfill foundation and footing trenches', subactivity: 'W1726 - Companion', workDone: '540', date: 'May 6, 2012' },
  { id: '01906912', activity: 'Excavate and prepare building foundation', subactivity: 'W1726 - Companion', workDone: '877', date: 'September 24, 2017' },
  { id: '76031847', activity: 'Place and finish site paving', subactivity: 'W9794 - Job Support', workDone: '922', date: 'May 31, 2015' },
  { id: '37890606', activity: 'Place and finish site paving', subactivity: 'W2025 - Supported Employment – Job Support', workDone: '536', date: 'February 9, 2015' },
  { id: '58276066', activity: 'Install underground utilities', subactivity: 'W7059 - HCH 1:2/In-Home & Community Support', workDone: '423', date: 'August 2, 2013' },
  { id: '01906912', activity: 'Mobilize construction equipment and personnel', subactivity: 'W7059 - HCH 1:2/In-Home & Community Support', workDone: '600', date: 'December 29, 2012' },
  { id: '01906912', activity: 'Place and finish site paving', subactivity: 'W7275 - Transportation Zone 2', workDone: '357', date: 'November 28, 2015' },
  { id: '37890606', activity: 'Install site lighting and electrical', subactivity: 'W7275 - Transportation Zone 2', workDone: '130', date: 'May 12, 2019' },
  { id: '37890606', activity: 'Install temporary facilities and controls', subactivity: 'SUMR - Summer Program', workDone: '883', date: 'March 6, 2018' },
  { id: '37890606', activity: 'Install site curbs and gutters', subactivity: 'W7059 - HCH 1:2/In-Home & Community Support', workDone: '185', date: 'May 9, 2014' },
  { id: '93242854', activity: 'Install site landscaping and irrigation', subactivity: 'W7059 - HCH 1:2/In-Home & Community Support', workDone: '426', date: 'February 29, 2012' },
  { id: '29103050', activity: 'Perform site clean-up and demobilization', subactivity: 'W2025 - Supported Employment – Job Support', workDone: '561', date: 'May 29, 2017' },
  { id: '93242854', activity: 'Install subgrade and base course for site paving', subactivity: 'SUMR - Summer Program', workDone: '740', date: 'October 25, 2019' },
  { id: '93242854', activity: 'Excavate and prepare building foundation', subactivity: 'SICK - Sick Day', workDone: '447', date: 'October 30, 2017' },
  { id: '34034474', activity: 'Install underground utilities', subactivity: 'W1726 - Companion', workDone: '994', date: 'March 23, 2013' },
  { id: '58276066', activity: 'Perform site clean-up and demobilization', subactivity: 'EXPL - Assessment', workDone: '453', date: 'February 11, 2014' },
  { id: '55069827', activity: 'Install site fencing and gates', subactivity: 'SICK - Sick Day', workDone: '816', date: 'February 28, 2018' },
  { id: '43397744', activity: 'Install site fencing and gates', subactivity: 'SICK - Sick Day', workDone: '583', date: 'February 28, 2018' },
  { id: '43397744', activity: 'Install site fencing and gates', subactivity: 'SICK - Sick Day', workDone: '429', date: 'February 28, 2018' },
];

const sampleColumns = [
  { data: 'id', title: 'ID', width: 150 },
  { data: 'activity', title: 'Activity', width: 300 },
  { data: 'subactivity', title: 'Subactivity', width: 300 },
  { data: 'workDone', title: 'Work Done', width: 150 },
  { data: 'date', title: 'Date', width: 150 },
];

const hotInstance = ref(null);
const isLoading = ref(false);

async function loadHandsontableFiles() {
  try {
    isLoading.value = true;

    // Load Handsontable CSS
    await load_js_css_file(
      'https://cdn.jsdelivr.net/npm/handsontable@15.3/styles/handsontable.min.css',
      'handsontable-css',
      'css',
    );

    // Load Handsontable theme CSS
    await load_js_css_file(
      'https://cdn.jsdelivr.net/npm/handsontable@15.3/styles/ht-theme-main.css',
      'handsontable-theme-css',
      'css',
    );

    isLoading.value = false;
  }
  catch (error) {
    console.error('Error loading Handsontable files:', error);
    isLoading.value = false;
  }
}
function customRenderer(instance, td, row, col, prop, value, cellProperties) {
  // Apply skeleton loader if needed
  if (props.showSkeletonLoader) {
    td.innerHTML = '<div class="skeleton-loader"></div>';
    td.className = 'skeleton-cell';
    return td;
  }

  // Default text renderer - use window.Handsontable when loaded dynamically
  if (window.Handsontable) {
    // eslint-disable-next-line prefer-rest-params
    window.Handsontable.renderers.TextRenderer.apply(this, arguments);
  }
  else {
    // Fallback if Handsontable is not loaded yet
    td.innerHTML = value || '';
  }

  // Add padding using inline styles (not CSS classes)
  td.style.padding = '8px 12px';
  td.style.textAlign = 'left';
  td.style.verticalAlign = 'middle';
  td.style.fontSize = '14px';
  td.style.lineHeight = '1.4';

  // Apply column background color if configured
  const columnKey = sampleColumns[col]?.data || sampleColumns[col]?.title || col;
  const columnColor = props.columnConfig[columnKey]?.backgroundColor;

  if (columnColor) {
    td.style.backgroundColor = columnColor;
  }

  // Make cells readonly
  cellProperties.readOnly = true;

  return td;
}

function createHeaderRenderer() {
  return function (col, TH) {
    const columnData = sampleColumns[col];
    const originalTitle = columnData?.title || `Column ${col + 1}`;
    const columnKey = sampleColumns[col]?.data || sampleColumns[col]?.title || col;
    const columnColor = props.columnConfig[columnKey]?.backgroundColor;

    // Add padding and styling directly to the header
    TH.style.padding = '8px 12px';
    TH.style.textAlign = 'left';

    if (columnColor) {
      TH.style.backgroundColor = columnColor;
    }

    TH.classList.add('group');

    TH.innerHTML = `
      <div class="header-wrapper flex items-center justify-between">
        <!-- Left side: Title + sort icon -->
        <div class="flex items-center gap-x-2">
          <div class="text-sm font-medium">${originalTitle}</div>
          <div class="column-action-btn flex gap-2 opacity-0 transition-opacity duration-200 group-hover:opacity-100">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="cursor-pointer" title="Sort">
              <path d="M17 4V20M17 20L13 16M17 20L21 16M7 20V4M7 4L3 8M7 4L11 8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>
        </div>

        <!-- Right side: filter icon -->
        <div class="column-action-btn flex gap-2 opacity-0 transition-opacity duration-200 group-hover:opacity-100">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="cursor-pointer" title="Filter">
            <path d="M2 4.6C2 4.03995 2 3.75992 2.10899 3.54601C2.20487 3.35785 2.35785 3.20487 2.54601 3.10899C2.75992 3 3.03995 3 3.6 3H20.4C20.9601 3 21.2401 3 21.454 3.10899C21.6422 3.20487 21.7951 3.35785 21.891 3.54601C22 3.75992 22 4.03995 22 4.6V5.26939C22 5.53819 22 5.67259 21.9672 5.79756C21.938 5.90831 21.8901 6.01323 21.8255 6.10776C21.7526 6.21443 21.651 6.30245 21.4479 6.4785L15.0521 12.0215C14.849 12.1975 14.7474 12.2856 14.6745 12.3922C14.6099 12.4868 14.562 12.5917 14.5328 12.7024C14.5 12.8274 14.5 12.9618 14.5 13.2306V18.4584C14.5 18.6539 14.5 18.7517 14.4685 18.8363C14.4406 18.911 14.3953 18.9779 14.3363 19.0315C14.2695 19.0922 14.1787 19.1285 13.9971 19.2012L10.5971 20.5612C10.2296 20.7082 10.0458 20.7817 9.89827 20.751C9.76927 20.7242 9.65605 20.6476 9.58325 20.5377C9.5 20.4122 9.5 20.2142 9.5 19.8184V13.2306C9.5 12.9618 9.5 12.8274 9.46715 12.7024C9.43805 12.5917 9.39014 12.4868 9.32551 12.3922C9.25258 12.2856 9.15102 12.1975 8.94789 12.0215L2.55211 6.4785C2.34898 6.30245 2.24742 6.21443 2.17449 6.10776C2.10986 6.01323 2.06195 5.90831 2.03285 5.79756C2 5.67259 2 5.53819 2 5.26939V4.6Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
      </div>
    `;

    // Add click event for header icons
    const icons = TH.querySelectorAll('.column-action-btn');
    icons.forEach((icon, index) => {
      icon.addEventListener('click', (e) => {
        e.stopPropagation();
        const actions = ['sort', 'filter'];
        emit('headerClick', { column: columnData, action: actions[index] });
      });
    });
  };
}

function afterColumnResize() {
  const columnWidths = [];
  for (let i = 0; i < hotInstance.value.countCols(); i++) {
    columnWidths.push(hotInstance.value.getColWidth(i));
  }
  emit('columnResize', columnWidths);
}

function initializeTable() {
  if (!window.BiHandsontable)
    return;

  const settings = {
    data: props.data.length > 0 ? props.data : sampleData,
    columns: props.columns.length > 0 ? props.columns : sampleColumns,
    manualColumnResize: true,
    autoColumnSize: false,
    height: props.height,
    width: props.width,

    licenseKey: 'non-commercial-and-evaluation',
    viewportRowRenderingOffset: 100,

    // Headers
    colHeaders: true,
    rowHeaders: false,
    manualColumnResize: true,
    themeName: 'ht-theme-main',

    // Key setting to stretch columns
    stretchH: 'all',

    // Table styling for clean look
    className: 'htLeft htMiddle',

    // Cell padding and spacing
    cellPadding: 12,
    rowHeaderWidth: 0,

    // Disable borders
    customBorders: false,

    // Nested headers support
    ...(props.nestedHeaders?.length && { nestedHeaders: props.nestedHeaders }),

    // Custom cell renderer
    cells() {
      return {
        renderer: customRenderer,
      };
    },

    // Column header renderer
    afterGetColHeader(col, TH) {
      createHeaderRenderer(col)(col, TH);
    },

    // Double click to autofit column
    afterOnCellMouseDown(event, coords, TD) {
      if (coords.row === -1 && event.detail === 2) { // Double click on header
        TD.addEventListener('dblclick', () => {
          if (!hotInstance.value)
            return;
          const col = coords.col;

          // Recalculate ideal width
          const autoSizePlugin = hotInstance.value.getPlugin('autoColumnSize');
          autoSizePlugin.calculateColumnsWidth(col, undefined, true);
          const newWidth = autoSizePlugin.getColumnWidth(col);

          // Apply new width
          const resizePlugin = hotInstance.value.getPlugin('manualColumnResize');
          resizePlugin.setManualSize(col, Math.max(100, newWidth));

          // Re-render
          hotInstance.value.render();

          // Emit updated columns
          afterColumnResize();
        }, { once: true });
      }
    },

    // Column resize callback
    afterColumnResize,

    // Cell click callback
    afterSelectionEnd(row, column, row2, column2) {
      emit('cellClick', { row, column, row2, column2 });
    },
  };
  const container = document.getElementById(props.biTableId);

  hotInstance.value = new window.BiHandsontable(container, settings);
  isLoading.value = false;
}

onMounted(async () => {
  isLoading.value = true;
  await loadHandsontableFiles();
  import('handsontable').then((module) => {
    if (!window.BiHandsontable) {
      window.BiHandsontable = module.default;
      initializeTable();
    }
    else {
      initializeTable();
    }
  });
});
</script>

<template>
  <HawkLoader v-if="isLoading" />
  <div class="bi-handsontable-wrapper">
    <div :id="biTableId" class="ht-theme-main" />
  </div>
</template>

<style scoped lang="scss">
.bi-handsontable-wrapper {
  &:deep(.handsontable tr td:first-child) {
    border-left: 1px solid #e0e0e0 !important;
  }
  &:deep(.handsontable th.ht__active_highlight) {
    background-color: rgb(26, 66, 232) !important;
  }

  // Simple row hover highlighting with CSS only
  &:deep(.handsontable tbody tr:hover td) {
    background-color: #f8f9fa !important;
    transition: background-color 0.15s ease;
  }
}

// Skeleton loader styles
:deep(.skeleton-loader) {
  width: 100%;
  height: 16px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4px;
}

:deep(.skeleton-cell) {
  padding: 8px !important;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}
</style>
