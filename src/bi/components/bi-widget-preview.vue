<script setup>
import { ChartHelper } from '@sensehawk/chart-generator';
import * as echarts from 'echarts';
import { storeToRefs } from 'pinia';
import { nextTick, onBeforeUnmount, watch } from 'vue';
import { BI_CHART_COLOR_PALETTES } from '~/bi/helpers/bi-helpers';
import { useBiStore } from '~/bi/store/bi.store';
import BiBottomDrawer from './bi-bottom-drawer.vue';

const emit = defineEmits(['continue']);

const bi_store = useBiStore();
const { chart_builder_config, chart_builder_data } = storeToRefs(bi_store);

const state = reactive({
  chart_instance: false,
  echarts_config: null,
});

function renderWidget() {
  if (chart_builder_config.value.chart_type === 'table') {
    // Probably some data processing
  }
  else {
    const helper = new ChartHelper();
    helper.setData(chart_builder_data.value, {
      category: 'Category',
      values: ['Planned', 'Actual'],
      // stackBy: 'Quarter',
    });
    helper.setChartType('bar');
    helper.setAxisNames('Custom category', 'Value in billion dollars');
    helper.setSeriesStyle('Planned', { lineStyle: 'dashed' });

    // Display tab
    helper.setTitle(chart_builder_config.value.title, chart_builder_config.value.subtitle);
    helper.setLegend({
      show: chart_builder_config.value.legend_position !== 'none',
      position: chart_builder_config.value.legend_position,
      orientation: chart_builder_config.value.legend_position === 'top' || chart_builder_config.value.legend_position === 'bottom' ? 'horizontal' : 'vertical',
    });
    helper.setDataValuesDisplay({
      displayValues: chart_builder_config.value.values === 'show',
    });
    helper.setColors(BI_CHART_COLOR_PALETTES[chart_builder_config.value.color_palette].colors);

    // Advanced tab
    helper.setAccessibilityPatterns(chart_builder_config.value.accessibility_patterns);
    helper.setOrientation(chart_builder_config.value.orientation);
    if (chart_builder_config.value.data_zoom !== 'disabled') {
      helper.setDataZoom(chart_builder_config.value.data_zoom);
    }
    // helper.setReferenceLines(chart_builder_config.value.reference_lines.map(line => ({
    //   value: line.reference_line,
    //   label: line.reference_line,
    //   color: 'red',
    // })));

    const echarts_config = helper.generateEChartsConfig();

    const chartElement = document.getElementById('chart-container');
    if (!state.chart_instance) {
      state.chart_instance = echarts.init(chartElement);
    }
    else if (state.chart_instance) {
      state.chart_instance.clear();
    }

    state.chart_instance.setOption(echarts_config);
  }
}

watch(() => chart_builder_config.value, async () => {
  await nextTick();
  renderWidget();
}, { deep: true, immediate: true });

onBeforeUnmount(() => {
  if (state.chart_instance) {
    state.chart_instance.dispose();
    state.chart_instance = null;
  }
});
</script>

<template>
  <div class="h-full w-full flex flex-col relative">
    <div class="p-6 flex-1">
      <div v-if="chart_builder_config.chart_type === 'table'">
        TABLE
      </div>
      <div v-else id="chart-container" class="h-full w-full" />
    </div>

    <hr>
    <div class="p-6 w-full flex justify-end items-center">
      <HawkButton @click="emit('continue')">
        <IconHawkRocketTwo />
        Publish widget
      </HawkButton>
    </div>

    <BiBottomDrawer
      show-text="Show results"
      hide-text="Hide results"
    >
      <template #default>
        TABLE
      </template>
    </BiBottomDrawer>
  </div>
</template>
